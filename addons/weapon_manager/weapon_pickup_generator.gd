@tool
extends RefCounted
class_name WeaponPickupGenerator

## Weapon Pickup Generator
## Creates weapon pickup scenes from weapon resources

static func generate_pickup_scene(weapon_resource: WeaponResource) -> PackedScene:
	var scene = PackedScene.new()
	var pickup_node = Area3D.new()
	pickup_node.name = weapon_resource.weapon_name + "_Pickup"
	pickup_node.set_script(preload("res://Core/Pickups/WeaponPickup.gd"))
	
	# Set pickup properties
	pickup_node.weapon_type = weapon_resource.weapon_name
	pickup_node.ammo_amount = weapon_resource.ammo_per_clip
	
	# Generate weapon scene for the pickup
	var weapon_scene = weapon_resource.generate_weapon_scene()
	pickup_node.weapon_resource = weapon_scene
	
	# Add collision shape
	var collision_shape = CollisionShape3D.new()
	collision_shape.name = "CollisionShape3D"
	var box_shape = BoxShape3D.new()
	box_shape.size = Vector3(1, 1, 1)
	collision_shape.shape = box_shape
	pickup_node.add_child(collision_shape)
	collision_shape.owner = pickup_node
	
	# Add visual mesh
	var mesh_instance = MeshInstance3D.new()
	mesh_instance.name = "MeshInstance3D"
	
	if weapon_resource.weapon_model:
		# Use the weapon model if available
		var model_instance = weapon_resource.weapon_model.instantiate()
		mesh_instance.add_child(model_instance)
		model_instance.owner = pickup_node
	else:
		# Create a simple box mesh as placeholder
		var box_mesh = BoxMesh.new()
		box_mesh.size = Vector3(0.5, 0.3, 0.1)
		mesh_instance.mesh = box_mesh
		
		# Add a simple material
		var material = StandardMaterial3D.new()
		material.albedo_color = Color.ORANGE
		material.emission_enabled = true
		material.emission = Color.ORANGE * 0.3
		mesh_instance.material_override = material
	
	pickup_node.add_child(mesh_instance)
	mesh_instance.owner = pickup_node
	
	# Add audio player
	var audio_player = AudioStreamPlayer3D.new()
	audio_player.name = "AudioStreamPlayer3D"
	pickup_node.add_child(audio_player)
	audio_player.owner = pickup_node
	
	# Pack the scene
	scene.pack(pickup_node)
	return scene

static func save_pickup_scene(weapon_resource: WeaponResource, save_path: String = "") -> String:
	if save_path.is_empty():
		save_path = "res://Core/Pickups/" + weapon_resource.weapon_name.to_lower().replace(" ", "_") + "_pickup.tscn"
	
	var pickup_scene = generate_pickup_scene(weapon_resource)
	var result = ResourceSaver.save(pickup_scene, save_path)
	
	if result == OK:
		print("Weapon pickup saved: ", save_path)
		return save_path
	else:
		print("Failed to save weapon pickup: ", result)
		return ""

static func create_pickup_in_scene(weapon_resource: WeaponResource, position: Vector3 = Vector3.ZERO) -> Node3D:
	var pickup_scene = generate_pickup_scene(weapon_resource)
	var pickup_instance = pickup_scene.instantiate()
	pickup_instance.global_position = position
	return pickup_instance
