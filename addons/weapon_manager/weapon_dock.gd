@tool
extends Control

## Weapon Manager Dock
## Main UI for managing weapons in the editor

const WeaponResource = preload("res://addons/weapon_manager/weapon_resource.gd")
const WeaponTemplates = preload("res://addons/weapon_manager/weapon_templates.gd")
const WeaponPickupGenerator = preload("res://addons/weapon_manager/weapon_pickup_generator.gd")
const WeaponIntegration = preload("res://addons/weapon_manager/weapon_integration.gd")

# UI Components
var main_container: VBoxContainer
var toolbar: HBoxContainer
var weapon_list: ItemList
var details_panel: VBoxContainer
var property_editor: VBoxContainer

# Buttons
var new_weapon_btn: Button
var duplicate_btn: Button
var delete_btn: Button
var save_btn: Button
var export_btn: Button
var generate_pickup_btn: Button
var validate_btn: Button

# Current weapon data
var current_weapon: WeaponResource
var weapons_data: Array[WeaponResource] = []
var weapons_directory: String = "res://Core/Weapons/Resources/"

func _init():
	name = "Weapons"
	custom_minimum_size = Vector2(280, 400)
	_setup_ui()
	_load_existing_weapons()

func _setup_ui():
	# Main container
	main_container = VBoxContainer.new()
	add_child(main_container)
	main_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	main_container.add_theme_constant_override("separation", 4)

	# Title
	var title = Label.new()
	title.text = "Weapon Manager"
	title.add_theme_font_size_override("font_size", 14)
	main_container.add_child(title)

	# Toolbar
	toolbar = HBoxContainer.new()
	toolbar.add_theme_constant_override("separation", 2)
	main_container.add_child(toolbar)
	
	new_weapon_btn = Button.new()
	new_weapon_btn.text = "New"
	new_weapon_btn.tooltip_text = "Create a new weapon"
	new_weapon_btn.custom_minimum_size.x = 50
	toolbar.add_child(new_weapon_btn)
	new_weapon_btn.pressed.connect(_on_new_weapon)

	duplicate_btn = Button.new()
	duplicate_btn.text = "Dup"
	duplicate_btn.tooltip_text = "Duplicate selected weapon"
	duplicate_btn.disabled = true
	duplicate_btn.custom_minimum_size.x = 40
	toolbar.add_child(duplicate_btn)
	duplicate_btn.pressed.connect(_on_duplicate_weapon)

	delete_btn = Button.new()
	delete_btn.text = "Del"
	delete_btn.tooltip_text = "Delete selected weapon"
	delete_btn.disabled = true
	delete_btn.custom_minimum_size.x = 40
	toolbar.add_child(delete_btn)
	delete_btn.pressed.connect(_on_delete_weapon)

	# Add spacer
	var spacer = Control.new()
	spacer.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	toolbar.add_child(spacer)

	# Refresh button
	var refresh_btn = Button.new()
	refresh_btn.text = "↻"
	refresh_btn.tooltip_text = "Refresh weapon list"
	refresh_btn.custom_minimum_size.x = 30
	toolbar.add_child(refresh_btn)
	refresh_btn.pressed.connect(_load_existing_weapons)

	# Convert button for testing
	var convert_btn = Button.new()
	convert_btn.text = "⚙"
	convert_btn.tooltip_text = "Convert existing weapon scenes"
	convert_btn.custom_minimum_size.x = 30
	toolbar.add_child(convert_btn)
	convert_btn.pressed.connect(_convert_existing_weapon_scenes)
	
	# Weapon list
	var list_label = Label.new()
	list_label.text = "Weapons:"
	list_label.add_theme_font_size_override("font_size", 12)
	main_container.add_child(list_label)

	weapon_list = ItemList.new()
	weapon_list.custom_minimum_size = Vector2(0, 120)
	weapon_list.size_flags_vertical = Control.SIZE_EXPAND_FILL
	main_container.add_child(weapon_list)
	weapon_list.item_selected.connect(_on_weapon_selected)

	# Details panel
	var details_label = Label.new()
	details_label.text = "Weapon Details:"
	details_label.add_theme_font_size_override("font_size", 12)
	main_container.add_child(details_label)

	var scroll_container = ScrollContainer.new()
	scroll_container.size_flags_vertical = Control.SIZE_EXPAND_FILL
	scroll_container.custom_minimum_size = Vector2(0, 150)
	main_container.add_child(scroll_container)

	details_panel = VBoxContainer.new()
	details_panel.add_theme_constant_override("separation", 2)
	details_panel.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	scroll_container.add_child(details_panel)
	
	# Bottom toolbar - use VBox for better layout
	var bottom_section = VBoxContainer.new()
	bottom_section.add_theme_constant_override("separation", 2)
	main_container.add_child(bottom_section)

	# First row of buttons
	var bottom_toolbar1 = HBoxContainer.new()
	bottom_toolbar1.add_theme_constant_override("separation", 2)
	bottom_section.add_child(bottom_toolbar1)

	save_btn = Button.new()
	save_btn.text = "Save"
	save_btn.tooltip_text = "Save current weapon"
	save_btn.disabled = true
	save_btn.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	bottom_toolbar1.add_child(save_btn)
	save_btn.pressed.connect(_on_save_weapon)

	export_btn = Button.new()
	export_btn.text = "Export"
	export_btn.tooltip_text = "Export weapon as scene file"
	export_btn.disabled = true
	export_btn.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	bottom_toolbar1.add_child(export_btn)
	export_btn.pressed.connect(_on_export_weapon)

	# Second row of buttons
	var bottom_toolbar2 = HBoxContainer.new()
	bottom_toolbar2.add_theme_constant_override("separation", 2)
	bottom_section.add_child(bottom_toolbar2)

	generate_pickup_btn = Button.new()
	generate_pickup_btn.text = "Pickup"
	generate_pickup_btn.tooltip_text = "Generate pickup scene for this weapon"
	generate_pickup_btn.disabled = true
	generate_pickup_btn.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	bottom_toolbar2.add_child(generate_pickup_btn)
	generate_pickup_btn.pressed.connect(_on_generate_pickup)

	validate_btn = Button.new()
	validate_btn.text = "Validate"
	validate_btn.tooltip_text = "Validate weapon configuration"
	validate_btn.disabled = true
	validate_btn.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	bottom_toolbar2.add_child(validate_btn)
	validate_btn.pressed.connect(_on_validate_weapon)

func _load_existing_weapons():
	weapons_data.clear()
	weapon_list.clear()

	# Ensure weapons directory exists
	if not DirAccess.dir_exists_absolute(weapons_directory):
		DirAccess.open("res://").make_dir_recursive(weapons_directory)

	# Load existing weapon resources (.tres files)
	_load_weapon_resources()

	# Convert existing weapon scenes (.tscn files) to resources
	_convert_existing_weapon_scenes()

	print("Loaded ", weapons_data.size(), " weapons")

func _load_weapon_resources():
	var dir = DirAccess.open(weapons_directory)
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		while file_name != "":
			if file_name.ends_with(".tres") and file_name.begins_with("weapon_"):
				var resource_path = weapons_directory + file_name
				var weapon_resource = load(resource_path) as WeaponResource
				if weapon_resource:
					weapons_data.append(weapon_resource)
					weapon_list.add_item(weapon_resource.weapon_name)
			file_name = dir.get_next()

func _convert_existing_weapon_scenes():
	var dir = DirAccess.open(weapons_directory)
	if not dir:
		print("Could not open weapons directory: ", weapons_directory)
		return

	dir.list_dir_begin()
	var file_name = dir.get_next()

	while file_name != "":
		if file_name.ends_with(".tscn") and not file_name.contains("pickup"):
			var scene_path = weapons_directory + file_name
			print("Attempting to convert weapon scene: ", scene_path)

			var scene = load(scene_path) as PackedScene
			if scene:
				var weapon_node = scene.instantiate()
				if weapon_node:
					print("Instantiated weapon node: ", weapon_node.name)

					# Check if it has weapon properties
					if "weapon_name" in weapon_node:
						var weapon_name = weapon_node.weapon_name
						print("Found weapon: ", weapon_name)

						# Check if we already have this weapon as a resource
						var already_exists = false
						for existing_weapon in weapons_data:
							if existing_weapon.weapon_name == weapon_name:
								already_exists = true
								break

						if not already_exists:
							print("Converting weapon: ", weapon_name)
							var weapon_resource = _create_weapon_resource_from_scene(weapon_node)
							if weapon_resource:
								print("Weapon resource created successfully")
								weapons_data.append(weapon_resource)
								weapon_list.add_item(weapon_resource.weapon_name)

								# Save the converted resource
								var resource_filename = "weapon_" + weapon_resource.weapon_name.to_lower().replace(" ", "_") + ".tres"
								var resource_path = weapons_directory + resource_filename
								print("Saving to: ", resource_path)
								var save_result = ResourceSaver.save(weapon_resource, resource_path)
								if save_result == OK:
									print("✓ Converted weapon scene to resource: ", weapon_resource.weapon_name)
								else:
									print("✗ Failed to save converted weapon: ", weapon_resource.weapon_name, " Error: ", save_result)
							else:
								print("✗ Failed to create weapon resource for: ", weapon_name)
						else:
							print("Weapon already exists as resource: ", weapon_name)
					else:
						print("Scene does not appear to be a weapon: ", file_name)

					weapon_node.queue_free()
				else:
					print("Failed to instantiate scene: ", scene_path)
			else:
				print("Failed to load scene: ", scene_path)

		file_name = dir.get_next()

func _create_weapon_resource_from_scene(weapon_node) -> WeaponResource:
	print("Creating weapon resource from scene node: ", weapon_node.name)

	# Try to create WeaponResource, return null if class not available
	var weapon_resource = null
	if ClassDB.class_exists("WeaponResource"):
		weapon_resource = ClassDB.instantiate("WeaponResource")
	else:
		# Fallback: try to load the script directly
		var script = load("res://addons/weapon_manager/weapon_resource.gd")
		if script:
			weapon_resource = script.new()

	if not weapon_resource:
		print("Failed to create WeaponResource - class not available")
		return null

	print("WeaponResource created successfully")

	# Copy basic properties with simple direct access
	weapon_resource.weapon_name = weapon_node.weapon_name
	weapon_resource.weapon_description = weapon_node.weapon_description if "weapon_description" in weapon_node else "Converted weapon"
	weapon_resource.damage = weapon_node.damage
	weapon_resource.fire_rate = weapon_node.fire_rate
	weapon_resource.reload_time = weapon_node.reload_time
	weapon_resource.ammo_per_clip = weapon_node.ammo_per_clip
	weapon_resource.max_ammo = weapon_node.max_ammo
	weapon_resource.auto_fire = weapon_node.auto_fire if "auto_fire" in weapon_node else false
	weapon_resource.spread = weapon_node.spread

	# Set weapon type based on name
	var name_lower = weapon_resource.weapon_name.to_lower()
	if "pistol" in name_lower or "pp8" in name_lower:
		weapon_resource.weapon_type = "Pistol"
		weapon_resource.is_dual_wieldable = true
	elif "shotgun" in name_lower:
		weapon_resource.weapon_type = "Shotgun"
	elif "rifle" in name_lower:
		weapon_resource.weapon_type = "Rifle"
	elif "smg" in name_lower:
		weapon_resource.weapon_type = "SMG"
	else:
		weapon_resource.weapon_type = "Pistol"

	# Copy audio and effects if available
	weapon_resource.fire_sound = weapon_node.fire_sound if "fire_sound" in weapon_node else null
	weapon_resource.reload_sound = weapon_node.reload_sound if "reload_sound" in weapon_node else null
	weapon_resource.empty_sound = weapon_node.empty_sound if "empty_sound" in weapon_node else null
	weapon_resource.equip_sound = weapon_node.equip_sound if "equip_sound" in weapon_node else null
	weapon_resource.muzzle_flash = weapon_node.muzzle_flash if "muzzle_flash" in weapon_node else null
	weapon_resource.impact_effect = weapon_node.impact_effect if "impact_effect" in weapon_node else null

	# Copy animation names
	weapon_resource.fire_animation = weapon_node.fire_animation if "fire_animation" in weapon_node else "fire"
	weapon_resource.reload_animation = weapon_node.reload_animation if "reload_animation" in weapon_node else "reload"
	weapon_resource.equip_animation = weapon_node.equip_animation if "equip_animation" in weapon_node else "equip"

	weapon_resource.template_used = "Converted from scene"

	return weapon_resource

func _on_new_weapon():
	var template_dialog = _create_template_dialog()
	add_child(template_dialog)
	template_dialog.popup_centered(Vector2i(400, 300))

func _create_template_dialog() -> AcceptDialog:
	var dialog = AcceptDialog.new()
	dialog.title = "Choose Weapon Template"
	
	var vbox = VBoxContainer.new()
	dialog.add_child(vbox)
	
	var label = Label.new()
	label.text = "Select a template for your new weapon:"
	vbox.add_child(label)
	
	var template_list = ItemList.new()
	template_list.custom_minimum_size = Vector2(350, 200)
	vbox.add_child(template_list)
	
	# Add templates
	var templates = WeaponTemplates.get_available_templates()
	for template in templates:
		template_list.add_item(template.name + " - " + template.description)
	
	var create_btn = Button.new()
	create_btn.text = "Create Weapon"
	vbox.add_child(create_btn)
	
	create_btn.pressed.connect(func():
		var selected = template_list.get_selected_items()
		if selected.size() > 0:
			var template = templates[selected[0]]
			_create_weapon_from_template(template)
		dialog.queue_free()
	)
	
	return dialog

func _create_weapon_from_template(template: Dictionary):
	var new_weapon = WeaponResource.new()

	# Apply template data
	new_weapon.weapon_name = template.get("name", "New Weapon")
	new_weapon.weapon_description = template.get("description", "A new weapon")
	new_weapon.damage = template.get("damage", 10.0)
	new_weapon.fire_rate = template.get("fire_rate", 1.0)
	new_weapon.reload_time = template.get("reload_time", 1.5)
	new_weapon.ammo_per_clip = template.get("ammo_per_clip", 10)
	new_weapon.max_ammo = template.get("max_ammo", 100)
	new_weapon.auto_fire = template.get("auto_fire", false)
	new_weapon.spread = template.get("spread", 0.0)
	new_weapon.weapon_type = template.get("weapon_type", "Pistol")
	new_weapon.is_dual_wieldable = template.get("is_dual_wieldable", false)
	new_weapon.template_used = template.get("name", "")

	# Add to list
	weapons_data.append(new_weapon)
	weapon_list.add_item(new_weapon.weapon_name)

	# Select the new weapon
	weapon_list.select(weapons_data.size() - 1)
	_on_weapon_selected(weapons_data.size() - 1)

func _on_duplicate_weapon():
	if current_weapon == null:
		return

	var duplicated = current_weapon.duplicate()
	duplicated.weapon_name += " Copy"
	duplicated.created_date = Time.get_datetime_string_from_system()
	duplicated.update_modified_time()

	weapons_data.append(duplicated)
	weapon_list.add_item(duplicated.weapon_name)

	# Select the duplicated weapon
	weapon_list.select(weapons_data.size() - 1)
	_on_weapon_selected(weapons_data.size() - 1)

func _on_delete_weapon():
	if current_weapon == null:
		return

	var selected_index = weapon_list.get_selected_items()[0]

	# Confirm deletion
	var confirm_dialog = ConfirmationDialog.new()
	confirm_dialog.dialog_text = "Are you sure you want to delete '" + current_weapon.weapon_name + "'?"
	add_child(confirm_dialog)

	confirm_dialog.confirmed.connect(func():
		# Remove from data
		weapons_data.remove_at(selected_index)
		weapon_list.remove_item(selected_index)

		# Clear current selection
		current_weapon = null
		_update_details_panel()
		_update_button_states()

		confirm_dialog.queue_free()
	)

	confirm_dialog.popup_centered()

func _on_weapon_selected(index: int):
	if index >= 0 and index < weapons_data.size():
		current_weapon = weapons_data[index]
		_update_details_panel()
		_update_button_states()

func _update_button_states():
	var has_selection = current_weapon != null
	duplicate_btn.disabled = not has_selection
	delete_btn.disabled = not has_selection
	save_btn.disabled = not has_selection
	export_btn.disabled = not has_selection
	generate_pickup_btn.disabled = not has_selection
	validate_btn.disabled = not has_selection

func _update_details_panel():
	# Clear existing controls
	for child in details_panel.get_children():
		child.queue_free()

	if current_weapon == null:
		var no_selection = Label.new()
		no_selection.text = "No weapon selected"
		details_panel.add_child(no_selection)
		return

	# Create property editors
	_create_property_editor("Name", "weapon_name", "string")
	_create_property_editor("Description", "weapon_description", "string")
	_create_property_editor("Type", "weapon_type", "enum")

	var separator1 = HSeparator.new()
	details_panel.add_child(separator1)

	_create_property_editor("Damage", "damage", "float")
	_create_property_editor("Fire Rate", "fire_rate", "float")
	_create_property_editor("Reload Time", "reload_time", "float")
	_create_property_editor("Ammo per Clip", "ammo_per_clip", "int")
	_create_property_editor("Max Ammo", "max_ammo", "int")
	_create_property_editor("Auto Fire", "auto_fire", "bool")
	_create_property_editor("Spread", "spread", "float")

	var separator2 = HSeparator.new()
	details_panel.add_child(separator2)

	_create_property_editor("Dual Wieldable", "is_dual_wieldable", "bool")
	_create_property_editor("Recoil Strength", "recoil_strength", "float")
	_create_property_editor("Max Range", "range_max", "float")

	# Model import section
	var separator3 = HSeparator.new()
	details_panel.add_child(separator3)

	var model_section = VBoxContainer.new()
	model_section.add_theme_constant_override("separation", 2)
	details_panel.add_child(model_section)

	var model_label = Label.new()
	model_label.text = "3D Model:"
	model_label.add_theme_font_size_override("font_size", 11)
	model_section.add_child(model_label)

	var model_path_label = Label.new()
	model_path_label.text = "No model" if current_weapon.weapon_model == null else str(current_weapon.weapon_model.resource_path).get_file()
	model_path_label.add_theme_font_size_override("font_size", 10)
	model_path_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	model_section.add_child(model_path_label)

	var browse_model_btn = Button.new()
	browse_model_btn.text = "Browse Model"
	browse_model_btn.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	model_section.add_child(browse_model_btn)
	browse_model_btn.pressed.connect(_on_browse_model)

func _create_property_editor(label_text: String, property_name: String, property_type: String):
	var container = VBoxContainer.new()
	container.add_theme_constant_override("separation", 1)
	details_panel.add_child(container)

	var label = Label.new()
	label.text = label_text + ":"
	label.add_theme_font_size_override("font_size", 11)
	container.add_child(label)

	var editor_control: Control

	match property_type:
		"string":
			var line_edit = LineEdit.new()
			line_edit.text = str(current_weapon.get(property_name))
			line_edit.size_flags_horizontal = Control.SIZE_EXPAND_FILL
			line_edit.text_changed.connect(func(new_text): _on_property_changed(property_name, new_text))
			editor_control = line_edit

		"int":
			var spin_box = SpinBox.new()
			spin_box.value = current_weapon.get(property_name)
			spin_box.step = 1
			spin_box.allow_greater = true
			spin_box.allow_lesser = true
			spin_box.size_flags_horizontal = Control.SIZE_EXPAND_FILL
			spin_box.value_changed.connect(func(new_value): _on_property_changed(property_name, int(new_value)))
			editor_control = spin_box

		"float":
			var spin_box = SpinBox.new()
			spin_box.value = current_weapon.get(property_name)
			spin_box.step = 0.1
			spin_box.allow_greater = true
			spin_box.allow_lesser = true
			spin_box.size_flags_horizontal = Control.SIZE_EXPAND_FILL
			spin_box.value_changed.connect(func(new_value): _on_property_changed(property_name, new_value))
			editor_control = spin_box

		"bool":
			var check_box = CheckBox.new()
			check_box.button_pressed = current_weapon.get(property_name)
			check_box.toggled.connect(func(pressed): _on_property_changed(property_name, pressed))
			editor_control = check_box

		"enum":
			var option_button = OptionButton.new()
			option_button.size_flags_horizontal = Control.SIZE_EXPAND_FILL
			var weapon_types = ["Pistol", "Rifle", "Shotgun", "SMG", "Sniper", "Heavy", "Special"]
			for type in weapon_types:
				option_button.add_item(type)
			var current_type = current_weapon.get(property_name)
			var type_index = weapon_types.find(current_type)
			if type_index >= 0:
				option_button.selected = type_index
			option_button.item_selected.connect(func(index): _on_property_changed(property_name, weapon_types[index]))
			editor_control = option_button

	if editor_control:
		container.add_child(editor_control)

func _on_property_changed(property_name: String, new_value):
	if current_weapon:
		current_weapon.set(property_name, new_value)
		current_weapon.update_modified_time()

		# Update weapon name in list if name changed
		if property_name == "weapon_name":
			var selected_index = weapon_list.get_selected_items()[0]
			weapon_list.set_item_text(selected_index, new_value)

func _on_browse_model():
	var file_dialog = FileDialog.new()
	file_dialog.file_mode = FileDialog.FILE_MODE_OPEN_FILE
	file_dialog.access = FileDialog.ACCESS_RESOURCES
	file_dialog.add_filter("*.tscn", "Scene Files")
	file_dialog.add_filter("*.glb", "GLTF Binary")
	file_dialog.add_filter("*.gltf", "GLTF Text")
	file_dialog.add_filter("*.obj", "Wavefront OBJ")
	file_dialog.add_filter("*.fbx", "FBX")

	add_child(file_dialog)
	file_dialog.file_selected.connect(_on_model_selected)
	file_dialog.popup_centered(Vector2i(800, 600))

func _on_model_selected(path: String):
	if current_weapon:
		if path.ends_with(".tscn"):
			current_weapon.weapon_model = load(path)
		else:
			# For 3D model files, we need to import them as scenes
			print("Model file selected: ", path)
			# TODO: Handle 3D model import and conversion to scene

		_update_details_panel()

	# Clean up the file dialog
	var file_dialog = get_children().filter(func(child): return child is FileDialog)
	for dialog in file_dialog:
		dialog.queue_free()

func _on_save_weapon():
	if current_weapon == null:
		return

	# Generate filename
	var filename = "weapon_" + current_weapon.weapon_name.to_lower().replace(" ", "_") + ".tres"
	var save_path = weapons_directory + filename

	# Save the resource
	var result = ResourceSaver.save(current_weapon, save_path)
	if result == OK:
		print("Weapon saved: ", save_path)
		# Show success message
		var dialog = AcceptDialog.new()
		dialog.dialog_text = "Weapon saved successfully!"
		add_child(dialog)
		dialog.popup_centered()
		dialog.confirmed.connect(func(): dialog.queue_free())
	else:
		print("Failed to save weapon: ", result)

func _on_export_weapon():
	if current_weapon == null:
		return

	# Generate weapon scene
	var weapon_scene = current_weapon.generate_weapon_scene()

	# Save scene
	var scene_filename = current_weapon.weapon_name.to_lower().replace(" ", "_") + ".tscn"
	var scene_path = weapons_directory + scene_filename

	var result = ResourceSaver.save(weapon_scene, scene_path)
	if result == OK:
		print("Weapon scene exported: ", scene_path)
		# Show success message
		var dialog = AcceptDialog.new()
		dialog.dialog_text = "Weapon scene exported successfully!\nPath: " + scene_path
		add_child(dialog)
		dialog.popup_centered()
		dialog.confirmed.connect(func(): dialog.queue_free())
	else:
		print("Failed to export weapon scene: ", result)

func _on_generate_pickup():
	if current_weapon == null:
		return

	# Generate pickup scene
	var pickup_scene = WeaponPickupGenerator.generate_pickup_scene(current_weapon)

	# Save pickup scene
	var pickup_filename = current_weapon.weapon_name.to_lower().replace(" ", "_") + "_pickup.tscn"
	var pickup_path = "res://Core/Pickups/" + pickup_filename

	var result = ResourceSaver.save(pickup_scene, pickup_path)
	if result == OK:
		print("Weapon pickup generated: ", pickup_path)
		# Show success message
		var dialog = AcceptDialog.new()
		dialog.dialog_text = "Weapon pickup generated successfully!\nPath: " + pickup_path
		add_child(dialog)
		dialog.popup_centered()
		dialog.confirmed.connect(func(): dialog.queue_free())
	else:
		print("Failed to generate weapon pickup: ", result)

func _on_validate_weapon():
	if current_weapon == null:
		return

	var issues = WeaponIntegration.validate_weapon_resource(current_weapon)

	var dialog = AcceptDialog.new()
	if issues.is_empty():
		dialog.dialog_text = "Weapon validation passed!\nNo issues found."
		dialog.title = "Validation Success"
	else:
		dialog.dialog_text = "Weapon validation failed!\n\nIssues found:\n• " + "\n• ".join(issues)
		dialog.title = "Validation Failed"

	add_child(dialog)
	dialog.popup_centered()
	dialog.confirmed.connect(func(): dialog.queue_free())
