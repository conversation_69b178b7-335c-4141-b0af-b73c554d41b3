@tool
extends RefCounted
class_name WeaponIntegration

## Weapon Integration Helper
## Provides utilities to integrate weapon manager with existing weapon system

static func register_weapon_in_database(weapon_resource: WeaponResource):
	"""Register a weapon resource in the game's weapon database"""
	if not Engine.is_editor_hint():
		var weapon_db = get_weapon_database()
		if weapon_db:
			weapon_db.register_weapon(
				weapon_resource.weapon_name,
				weapon_resource.max_ammo,
				weapon_resource.ammo_per_clip
			)

static func get_weapon_database():
	"""Get the weapon database singleton"""
	if Engine.has_singleton("WeaponDB"):
		return Engine.get_singleton("WeaponDB")
	return null

static func create_weapon_manager_entry(weapon_resource: WeaponResource) -> Dictionary:
	"""Create a weapon manager compatible entry"""
	return {
		"name": weapon_resource.weapon_name,
		"scene_path": "",  # Will be set when scene is generated
		"damage": weapon_resource.damage,
		"fire_rate": weapon_resource.fire_rate,
		"reload_time": weapon_resource.reload_time,
		"ammo_per_clip": weapon_resource.ammo_per_clip,
		"max_ammo": weapon_resource.max_ammo,
		"auto_fire": weapon_resource.auto_fire,
		"spread": weapon_resource.spread,
		"weapon_type": weapon_resource.weapon_type,
		"is_dual_wieldable": weapon_resource.is_dual_wieldable
	}

static func update_existing_weapon_scenes():
	"""Update existing weapon scenes to match current weapon resources"""
	var weapons_dir = "res://Core/Weapons/Resources/"
	var dir = DirAccess.open(weapons_dir)
	
	if not dir:
		print("Could not access weapons directory: ", weapons_dir)
		return
	
	dir.list_dir_begin()
	var file_name = dir.get_next()
	
	while file_name != "":
		if file_name.ends_with(".tres") and file_name.begins_with("weapon_"):
			var resource_path = weapons_dir + file_name
			var weapon_resource = load(resource_path) as WeaponResource
			
			if weapon_resource:
				# Generate updated scene
				var weapon_scene = weapon_resource.generate_weapon_scene()
				var scene_path = weapons_dir + weapon_resource.weapon_name.to_lower().replace(" ", "_") + ".tscn"
				
				var result = ResourceSaver.save(weapon_scene, scene_path)
				if result == OK:
					print("Updated weapon scene: ", scene_path)
				else:
					print("Failed to update weapon scene: ", scene_path)
		
		file_name = dir.get_next()

static func validate_weapon_resource(weapon_resource: WeaponResource) -> Array[String]:
	"""Validate a weapon resource and return any issues found"""
	var issues: Array[String] = []
	
	if weapon_resource.weapon_name.is_empty():
		issues.append("Weapon name cannot be empty")
	
	if weapon_resource.damage <= 0:
		issues.append("Damage must be greater than 0")
	
	if weapon_resource.fire_rate <= 0:
		issues.append("Fire rate must be greater than 0")
	
	if weapon_resource.reload_time < 0:
		issues.append("Reload time cannot be negative")
	
	if weapon_resource.ammo_per_clip <= 0:
		issues.append("Ammo per clip must be greater than 0")
	
	if weapon_resource.max_ammo < weapon_resource.ammo_per_clip:
		issues.append("Max ammo must be at least equal to ammo per clip")
	
	if weapon_resource.spread < 0:
		issues.append("Spread cannot be negative")
	
	return issues

static func export_weapon_data_json(weapon_resources: Array[WeaponResource], file_path: String = "res://weapon_data.json"):
	"""Export weapon data to JSON for external tools or backup"""
	var weapon_data = []
	
	for weapon in weapon_resources:
		var data = {
			"name": weapon.weapon_name,
			"description": weapon.weapon_description,
			"type": weapon.weapon_type,
			"damage": weapon.damage,
			"fire_rate": weapon.fire_rate,
			"reload_time": weapon.reload_time,
			"ammo_per_clip": weapon.ammo_per_clip,
			"max_ammo": weapon.max_ammo,
			"auto_fire": weapon.auto_fire,
			"spread": weapon.spread,
			"is_dual_wieldable": weapon.is_dual_wieldable,
			"recoil_strength": weapon.recoil_strength,
			"range_max": weapon.range_max,
			"created_date": weapon.created_date,
			"last_modified": weapon.last_modified,
			"template_used": weapon.template_used
		}
		weapon_data.append(data)
	
	var json_string = JSON.stringify(weapon_data, "\t")
	var file = FileAccess.open(file_path, FileAccess.WRITE)
	
	if file:
		file.store_string(json_string)
		file.close()
		print("Weapon data exported to: ", file_path)
		return true
	else:
		print("Failed to export weapon data to: ", file_path)
		return false

static func import_weapon_data_json(file_path: String) -> Array[WeaponResource]:
	"""Import weapon data from JSON"""
	var weapon_resources: Array[WeaponResource] = []
	var file = FileAccess.open(file_path, FileAccess.READ)
	
	if not file:
		print("Could not open file: ", file_path)
		return weapon_resources
	
	var json_string = file.get_as_text()
	file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result != OK:
		print("Failed to parse JSON: ", json.get_error_message())
		return weapon_resources
	
	var weapon_data = json.data
	
	for data in weapon_data:
		var weapon = WeaponResource.new()
		weapon.weapon_name = data.get("name", "Unknown")
		weapon.weapon_description = data.get("description", "")
		weapon.weapon_type = data.get("type", "Pistol")
		weapon.damage = data.get("damage", 10.0)
		weapon.fire_rate = data.get("fire_rate", 1.0)
		weapon.reload_time = data.get("reload_time", 1.5)
		weapon.ammo_per_clip = data.get("ammo_per_clip", 10)
		weapon.max_ammo = data.get("max_ammo", 100)
		weapon.auto_fire = data.get("auto_fire", false)
		weapon.spread = data.get("spread", 0.0)
		weapon.is_dual_wieldable = data.get("is_dual_wieldable", false)
		weapon.recoil_strength = data.get("recoil_strength", 1.0)
		weapon.range_max = data.get("range_max", 1000.0)
		weapon.created_date = data.get("created_date", "")
		weapon.last_modified = data.get("last_modified", "")
		weapon.template_used = data.get("template_used", "")
		
		weapon_resources.append(weapon)
	
	print("Imported ", weapon_resources.size(), " weapons from JSON")
	return weapon_resources
