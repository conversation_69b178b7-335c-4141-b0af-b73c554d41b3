@tool
extends EditorPlugin

## Weapon Manager Plugin
## Provides a comprehensive weapon management system for FPS games

const WeaponDock = preload("res://addons/weapon_manager/weapon_dock.gd")
const WeaponResource = preload("res://addons/weapon_manager/weapon_resource.gd")

var dock

func _enter_tree():
	print("Weapon Manager Plugin: Initializing...")

	# Add the custom weapon resource type first
	add_custom_type(
		"WeaponResource",
		"Resource",
		WeaponResource,
		preload("res://addons/weapon_manager/icons/weapon_icon.svg")
	)

	# Force refresh the filesystem to ensure class is registered
	EditorInterface.get_resource_filesystem().scan()

	# Wait a frame to ensure the class is registered
	await get_tree().process_frame

	# Create and add the weapon dock
	dock = WeaponDock.new()
	add_control_to_dock(DOCK_SLOT_LEFT_UL, dock)

	print("Weapon Manager Plugin: Initialized successfully!")

func _exit_tree():
	print("Weapon Manager Plugin: Cleaning up...")
	
	# Remove the custom type
	remove_custom_type("WeaponResource")
	
	# Remove the dock
	if dock:
		remove_control_from_docks(dock)
		dock.queue_free()
		dock = null
	
	print("Weapon Manager Plugin: Cleanup complete!")

func _has_main_screen():
	return false

func _get_plugin_name():
	return "Weapon Manager"
