# Weapon Manager Plugin

A comprehensive weapon management system for Godot FPS games that streamlines weapon creation, editing, and integration.

## Features

### 🔫 Weapon Management
- **Visual Weapon Editor**: Create and edit weapons through an intuitive dock interface
- **Template System**: Start with predefined weapon templates (Pistol, Rifle, Shotgun, SMG, Sniper, Heavy, Special)
- **Property Editor**: Modify weapon stats without touching code files
- **Validation System**: Ensure weapon configurations are valid before export

### 🎮 Game Integration
- **Automatic Scene Generation**: Generate complete weapon scenes from resources
- **Pickup Generation**: Create weapon pickup scenes automatically
- **Database Integration**: Seamlessly integrates with existing weapon database system
- **Dual-Wield Support**: Built-in support for dual-wieldable weapons

### 🛠️ Development Tools
- **Import/Export**: JSON import/export for backup and external tool integration
- **Model Support**: Import 3D models for weapons (GLTF, OBJ, FBX, etc.)
- **Resource Management**: Organized weapon resource storage
- **Live Preview**: See weapon properties in real-time

## Installation

1. Copy the `addons/weapon_manager` folder to your project's `addons/` directory
2. Enable the plugin in Project Settings > Plugins
3. The "Weapons" dock will appear in the editor

## Usage

### Creating a New Weapon

1. Click "New" in the Weapons dock
2. Choose a template from the dialog
3. Modify weapon properties in the details panel
4. Click "Save" to save the weapon resource
5. Click "Export Scene" to generate the weapon scene file

### Weapon Properties

- **Basic Info**: Name, description, type, icon
- **Combat Stats**: Damage, fire rate, reload time, ammo capacity
- **Behavior**: Auto-fire, spread, dual-wield capability
- **Advanced**: Recoil, range, penetration, knockback

### Generating Pickups

1. Select a weapon in the list
2. Click "Generate Pickup"
3. A pickup scene will be created in `res://Core/Pickups/`

### Model Import

1. Select a weapon
2. Click "Browse" next to the model field
3. Choose a 3D model file or scene
4. The model will be integrated into the weapon

## File Structure

```
addons/weapon_manager/
├── plugin.cfg                    # Plugin configuration
├── plugin.gd                     # Main plugin script
├── weapon_dock.gd                # Dock UI implementation
├── weapon_resource.gd            # Weapon resource class
├── weapon_templates.gd           # Predefined templates
├── weapon_pickup_generator.gd    # Pickup scene generator
├── weapon_integration.gd         # Game integration utilities
├── icons/
│   └── weapon_icon.svg          # Resource icon
└── README.md                     # This file
```

## Integration with Existing System

The plugin integrates with your existing weapon system:

- **WeaponDatabase**: Automatically registers weapons
- **WeaponManager**: Compatible with existing weapon switching
- **WeaponPickup**: Generates compatible pickup scenes
- **Weapon.gd**: Uses your existing weapon base class

## Templates

### Available Templates

1. **Basic Pistol**: Standard sidearm, dual-wieldable
2. **Assault Rifle**: Automatic rifle with moderate damage
3. **Combat Shotgun**: High damage, close range
4. **SMG**: Fast-firing, low damage
5. **Sniper Rifle**: High damage, long range, slow fire
6. **Heavy Machine Gun**: High damage automatic
7. **Rocket Launcher**: Explosive projectile weapon
8. **Energy Weapon**: Futuristic energy-based weapon

## Tips

- Use validation to check weapon balance before export
- Save weapon resources frequently during editing
- Generate pickups after finalizing weapon stats
- Use templates as starting points for similar weapons
- Export to JSON for backup or sharing configurations

## Troubleshooting

### Plugin Not Loading
- Ensure the plugin is enabled in Project Settings > Plugins
- Check that all files are in the correct directory structure
- Restart Godot editor after enabling the plugin

### WeaponResource Class Error
- If you see "Cannot get class 'WeaponResource'" error, restart Godot
- The plugin needs to register the custom class on first load

### Existing Weapons Not Showing
- Click the refresh button (↻) in the weapons dock
- The plugin automatically converts .tscn weapon files to .tres resources
- Check the console for conversion messages

### UI Layout Issues
- The dock is designed for the left sidebar
- If buttons are cut off, try resizing the dock panel
- Use the scroll area in the details panel for long property lists

### Weapon Scenes Not Working
- Verify the weapon extends the correct base class
- Check that required child nodes are present
- Ensure animations and audio files are properly assigned

### Model Import Issues
- Supported formats: .tscn, .glb, .gltf, .obj, .fbx
- Ensure models are properly imported in Godot
- Check model scale and orientation

### Conversion Issues
- If existing weapons don't convert properly, check the console output
- Ensure your weapon scenes have the standard weapon properties
- The converter looks for weapon_name, damage, fire_rate, etc.

## Contributing

This plugin is designed specifically for the Yakeru FPS Game project. Modifications should maintain compatibility with the existing weapon system.
