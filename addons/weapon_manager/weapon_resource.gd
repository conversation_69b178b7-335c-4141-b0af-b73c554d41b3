@tool
extends Resource
class_name WeaponResource

## Weapon Resource
## Stores all weapon configuration data for the weapon management system

@export_group("Basic Info")
@export var weapon_name: String = "New Weapon"
@export var weapon_description: String = "A new weapon"
@export var weapon_icon: Texture2D
@export var weapon_model: PackedScene

@export_group("Weapon Stats")
@export var damage: float = 10.0
@export var fire_rate: float = 1.0  # Shots per second
@export var reload_time: float = 1.5  # Seconds
@export var ammo_per_clip: int = 10
@export var max_ammo: int = 100
@export var auto_fire: bool = false
@export var spread: float = 0.0  # Bullet spread in degrees

@export_group("Weapon Type")
@export_enum("Pistol", "Rifle", "Shotgun", "SMG", "Sniper", "Heavy", "Special") var weapon_type: String = "Pistol"
@export var is_dual_wieldable: bool = false
@export var projectile_type: String = "hitscan"  # hitscan, projectile, beam

@export_group("Audio")
@export var fire_sound: AudioStream
@export var reload_sound: AudioStream
@export var empty_sound: AudioStream
@export var equip_sound: AudioStream

@export_group("Effects")
@export var muzzle_flash: PackedScene
@export var impact_effect: PackedScene
@export var shell_casing: PackedScene

@export_group("Animation")
@export var fire_animation: String = "fire"
@export var reload_animation: String = "reload"
@export var equip_animation: String = "equip"
@export var idle_animation: String = "idle"

@export_group("Advanced")
@export var recoil_strength: float = 1.0
@export var range_max: float = 1000.0
@export var penetration: int = 0  # How many objects it can penetrate
@export var knockback_force: float = 0.0

# Internal data
var created_date: String = ""
var last_modified: String = ""
var template_used: String = ""

func _init():
	created_date = Time.get_datetime_string_from_system()
	last_modified = created_date

func update_modified_time():
	last_modified = Time.get_datetime_string_from_system()

## Generate a weapon scene from this resource
func generate_weapon_scene() -> PackedScene:
	var scene = PackedScene.new()
	var weapon_node = Node3D.new()
	weapon_node.name = weapon_name
	weapon_node.set_script(preload("res://Core/Weapons/Weapon.gd"))
	
	# Set weapon properties
	weapon_node.weapon_name = weapon_name
	weapon_node.weapon_description = weapon_description
	weapon_node.weapon_icon = weapon_icon
	weapon_node.damage = damage
	weapon_node.fire_rate = fire_rate
	weapon_node.reload_time = reload_time
	weapon_node.ammo_per_clip = ammo_per_clip
	weapon_node.max_ammo = max_ammo
	weapon_node.auto_fire = auto_fire
	weapon_node.spread = spread
	
	# Set audio
	weapon_node.fire_sound = fire_sound
	weapon_node.reload_sound = reload_sound
	weapon_node.empty_sound = empty_sound
	weapon_node.equip_sound = equip_sound
	
	# Set effects
	weapon_node.muzzle_flash = muzzle_flash
	weapon_node.impact_effect = impact_effect
	
	# Set animations
	weapon_node.fire_animation = fire_animation
	weapon_node.reload_animation = reload_animation
	weapon_node.equip_animation = equip_animation
	
	# Add required child nodes
	_add_weapon_components(weapon_node)
	
	# Pack the scene
	scene.pack(weapon_node)
	return scene

func _add_weapon_components(weapon_node: Node3D):
	# Add MeshInstance3D if model is provided
	if weapon_model:
		var model_instance = weapon_model.instantiate()
		weapon_node.add_child(model_instance)
		model_instance.owner = weapon_node
	else:
		# Add default mesh
		var mesh_instance = MeshInstance3D.new()
		mesh_instance.name = "MeshInstance3D"
		var box_mesh = BoxMesh.new()
		box_mesh.size = Vector3(0.1, 0.1, 0.3)
		mesh_instance.mesh = box_mesh
		weapon_node.add_child(mesh_instance)
		mesh_instance.owner = weapon_node
	
	# Add RayCast3D for hitscan
	var raycast = RayCast3D.new()
	raycast.name = "RayCast3D"
	raycast.target_position = Vector3(0, 0, -range_max)
	weapon_node.add_child(raycast)
	raycast.owner = weapon_node
	
	# Add MuzzlePosition
	var muzzle_pos = Node3D.new()
	muzzle_pos.name = "MuzzlePosition"
	muzzle_pos.position = Vector3(0, 0, -0.3)
	weapon_node.add_child(muzzle_pos)
	muzzle_pos.owner = weapon_node
	
	# Add AudioStreamPlayer3D
	var audio_player = AudioStreamPlayer3D.new()
	audio_player.name = "AudioStreamPlayer3D"
	weapon_node.add_child(audio_player)
	audio_player.owner = weapon_node
	
	# Add AnimationPlayer
	var anim_player = AnimationPlayer.new()
	anim_player.name = "AnimationPlayer"
	weapon_node.add_child(anim_player)
	anim_player.owner = weapon_node
